<div x-data="{ viewMode: $wire.entangle('layout_view').live }">
    <!-- Search Header -->
    <div class="bg-white rounded-2xl shadow-lg p-6 mb-8">
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div class="md:col-span-2">
                <input 
                    type="text" 
                    placeholder="Search by location..."
                    wire:model.live.debounce.300ms="keywords"
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900"
                >
            </div>
            <select wire:model.live="property_type" class="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900">
                <option value="">All Types</option>
                <option value="apartment">Apartment</option>
                <option value="house">House</option>
                <option value="land">Land</option>
                <option value="single_room">Single Room</option>
            </select>
            <select wire:model.live="listing_type" class="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900">
                <option value="">For Sale & Rent</option>
                <option value="for_sale">For Sale</option>
                <option value="for_rent">For Rent</option>
            </select>
            <button wire:click="render" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                <i class="fas fa-search mr-2"></i>
                Search
            </button>
        </div>
    </div>

    <!-- Results Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Search Results</h1>
            <p class="text-gray-600">{{ $total_properties }} properties found</p>
        </div>
        <div class="flex items-center space-x-4">
            <div class="flex bg-gray-100 rounded-lg p-1">
                <button @click="viewMode = 'grid'" wire:click="$set('layout_view', 'grid')" class="px-3 py-2 rounded-md text-sm font-medium transition-colors" :class="viewMode === 'grid' ? 'bg-white text-gray-900 shadow' : 'text-gray-600'">
                    <i class="fas fa-th-large"></i> <span class="hidden sm:inline ml-1">Grid</span>
                </button>
                <button @click="viewMode = 'list'" wire:click="$set('layout_view', 'list')" class="px-3 py-2 rounded-md text-sm font-medium transition-colors" :class="viewMode === 'list' ? 'bg-white text-gray-900 shadow' : 'text-gray-600'">
                    <i class="fas fa-list"></i> <span class="hidden sm:inline ml-1">List</span>
                </button>
                <button @click="viewMode = 'map'" wire:click="$set('layout_view', 'map')" class="px-3 py-2 rounded-md text-sm font-medium transition-colors" :class="viewMode === 'map' ? 'bg-white text-gray-900 shadow' : 'text-gray-600'">
                    <i class="fas fa-map-marked-alt"></i> <span class="hidden sm:inline ml-1">Map</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Property Listings -->
    <div wire:loading.class="opacity-50" class="transition-opacity duration-300">
        <div x-show="viewMode === 'map'" class="mb-6">
            <div id="searchMap" 
                 style="height: 500px;" 
                 class="rounded-lg border border-gray-300 shadow-sm"
                 data-properties="@json($properties->items())"></div>
        </div>

        @if ($properties->isEmpty())
            <div x-show="viewMode !== 'map'" class="bg-white shadow-md rounded-lg p-8 text-center text-gray-600">
                <p>No properties found matching your criteria.</p>
            </div>
        @else
            <div x-show="viewMode === 'grid'">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    @foreach ($properties as $property)
                        <div class="bg-white rounded-xl shadow-lg overflow-hidden card-hover cursor-pointer" wire:click="showProperty({{ $property->id }})">
                            <div class="relative">
                                @if (!empty($property->images) && is_array($property->images))
                                    <img src="{{ Storage::url($property->images[0]) }}" alt="{{ $property->title }}" class="w-full h-48 object-cover">
                                @else
                                    <img src="https://via.placeholder.com/400x300?text=No+Image" alt="No Image" class="w-full h-48 object-cover">
                                @endif
                                <div class="absolute top-3 left-3">
                                    <span class="bg-blue-600 text-white px-2 py-1 rounded-full text-xs font-medium">{{ $property->listing_type === 'for_sale' ? 'For Sale' : 'For Rent' }}</span>
                                </div>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-bold text-gray-900 mb-1">{{ $property->title }}</h3>
                                <p class="text-gray-600 text-sm mb-2">{{ $property->city }}, {{ $property->state_region }}</p>
                                <div class="text-xl font-bold text-blue-600 mb-3">
                                    ${{ number_format($property->price) }}
                                    <span class="text-sm text-gray-500">{{ $property->listing_type === 'for_rent' ? '/month' : '' }}</span>
                                </div>
                                <div class="flex items-center text-sm text-gray-600 space-x-3">
                                    @if (isset($property->features['bedrooms']))
                                        <div class="flex items-center">
                                            <i class="fas fa-bed mr-1"></i>
                                            <span>{{ $property->features['bedrooms'] }}</span>
                                        </div>
                                    @endif
                                    @if (isset($property->features['bathrooms']))
                                        <div class="flex items-center">
                                            <i class="fas fa-bath mr-1"></i>
                                            <span>{{ $property->features['bathrooms'] }}</span>
                                        </div>
                                    @endif
                                    @if (isset($property->features['square_footage']))
                                        <div class="flex items-center">
                                            <i class="fas fa-ruler-combined mr-1"></i>
                                            <span>{{ $property->features['square_footage'] }}</span>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
            <div x-show="viewMode === 'list'">
                <div class="space-y-6">
                    @foreach ($properties as $property)
                        <div class="bg-white rounded-xl shadow-lg overflow-hidden card-hover cursor-pointer" wire:click="showProperty({{ $property->id }})">
                            <div class="md:flex">
                                <div class="md:w-1/3">
                                    @if (!empty($property->images) && is_array($property->images))
                                        <img src="{{ Storage::url($property->images[0]) }}" alt="{{ $property->title }}" class="w-full h-48 md:h-full object-cover">
                                    @else
                                        <img src="https://via.placeholder.com/400x300?text=No+Image" alt="No Image" class="w-full md:w-1/3 h-48 md:h-full object-cover">
                                    @endif
                                </div>
                                <div class="md:w-2/3 p-6">
                                    <div class="flex justify-between items-start mb-2">
                                        <h3 class="text-xl font-bold text-gray-900">{{ $property->title }}</h3>
                                        <span class="bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium">{{ $property->listing_type === 'for_sale' ? 'For Sale' : 'For Rent' }}</span>
                                    </div>
                                    <p class="text-gray-600 mb-3">{{ $property->city }}, {{ $property->state_region }}</p>
                                    <p class="text-gray-700 mb-4 line-clamp-3">{{ $property->description }}</p>
                                    <div class="flex justify-between items-center">
                                        <div class="text-2xl font-bold text-blue-600">
                                            ${{ number_format($property->price) }}
                                            <span class="text-sm text-gray-500">{{ $property->listing_type === 'for_rent' ? '/month' : '' }}</span>
                                        </div>
                                        <div class="flex items-center text-sm text-gray-600 space-x-4">
                                            @if (isset($property->features['bedrooms']))
                                                <div class="flex items-center">
                                                    <i class="fas fa-bed mr-1"></i>
                                                    <span>{{ $property->features['bedrooms'] }}</span>
                                                </div>
                                            @endif
                                            @if (isset($property->features['bathrooms']))
                                                <div class="flex items-center">
                                                    <i class="fas fa-bath mr-1"></i>
                                                    <span>{{ $property->features['bathrooms'] }}</span>
                                                </div>
                                            @endif
                                            @if (isset($property->features['square_footage']))
                                                <div class="flex items-center">
                                                    <i class="fas fa-ruler-combined mr-1"></i>
                                                    <span>{{ $property->features['square_footage'] }} sqft</span>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif

        <div class="mt-8">
            {{ $properties->links() }}
        </div>
    </div>
</div>

@push('scripts')
<script>
    document.addEventListener('livewire:navigated', () => {
        initSearchMap();
    });

    // Re-initialize map when Livewire component updates, specifically when properties might change
    // or viewMode changes to 'map'.
    // Using a custom event dispatched from Livewire component would be more robust,
    // but for now, we'll try to hook into a general update or use Alpine's $watch.
    
    let searchMapInstance = null;
    let propertyMarkers = [];

    function initSearchMap() {
        console.log('Initializing search map...');

        const mapElement = document.getElementById('searchMap');
        if (!mapElement) {
            console.error('Map element not found');
            return;
        }

        // Check if map container is visible
        const mapContainer = mapElement.parentNode;
        if (window.getComputedStyle(mapContainer).display === 'none') {
            console.log('Map container is hidden, skipping initialization');
            return;
        }

        console.log('Map element found and visible');

        // Clear previous markers
        if (searchMapInstance) {
            console.log('Clearing existing markers');
            propertyMarkers.forEach(marker => searchMapInstance.removeLayer(marker));
            propertyMarkers = [];
        } else {
            console.log('Creating new map instance');
            // Initialize map only once or if it was destroyed
            searchMapInstance = L.map(mapElement).setView([39.8283, -98.5795], 4); // Center of USA as default
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
                maxZoom: 19
            }).addTo(searchMapInstance);

            console.log('Map instance created successfully');
        }
        
        const propertiesDataString = mapElement.dataset.properties;
        let propertiesData = [];
        if (propertiesDataString) {
            try {
                propertiesData = JSON.parse(propertiesDataString);
            } catch (e) {
                console.error("Error parsing properties data for map:", e);
                mapElement.innerHTML = '<div class="p-4 text-center text-gray-500">Error: Could not load property data for map.</div>';
                return;
            }
        }
        
        let validPropertiesForMap = [];

        propertiesData.forEach(property => {
            if (property.latitude && property.longitude) {
                const lat = parseFloat(property.latitude);
                const lng = parseFloat(property.longitude);

                if (!isNaN(lat) && !isNaN(lng)) {
                    validPropertiesForMap.push(property); // Keep track for bounds
                    const marker = L.marker([lat, lng]).addTo(searchMapInstance)
                        .bindPopup(`<b>${property.title}</b><br><a href="/properties/${property.id}" class="text-blue-600 hover:underline">View Details</a>`);
                    propertyMarkers.push(marker);
                }
            }
        });

        if (validPropertiesForMap.length > 0) {
            const group = new L.featureGroup(propertyMarkers);
            searchMapInstance.fitBounds(group.getBounds().pad(0.3)); // Pad to ensure markers aren't on edge
        } else {
             // Attempt to geolocate user for better default view, or use a default
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(function(position) {
                    searchMapInstance.setView([position.coords.latitude, position.coords.longitude], 10);
                }, function() {
                    searchMapInstance.setView([20, 0], 2); // Wide default if geolocation fails
                });
            } else {
                searchMapInstance.setView([20, 0], 2); // Wide default if no geolocation
            }
        }
    }

    // Watch for viewMode changes using AlpineJS, if available in this context
    // This assumes the x-data is on a parent element that includes this script scope.
    // A more direct Livewire event or hook would be better.
    // For now, let's rely on Livewire's update cycle to re-render this script block
    // or use a specific Livewire event if we dispatch one.

    // This will re-run when Livewire re-renders this component's view.
    // It's important that the script is idempotent or handles re-initialization correctly.
    // The current initSearchMap tries to handle this by clearing markers and re-using the map instance.
    
    // A simple way to trigger re-init on Livewire update for this component
    // This might be too frequent if other things update.
    // A more targeted approach is to listen for a specific event or use wire:init on the map div.
    // For now, let's assume this script block re-runs on relevant updates.
    
    // If Alpine is managing viewMode, we can watch it.
    // This needs to be inside an Alpine component scope.
    // document.addEventListener('alpine:initialized', () => {
    //     Alpine.watch('viewMode', value => { // Assuming viewMode is accessible
    //         if (value === 'map') {
    //             setTimeout(initSearchMap, 0); // Ensure DOM is ready
    //         }
    //     });
    // });

    // A more robust way for Livewire:
    // In your Livewire component: $this->dispatch('propertiesUpdated');
    // In JS: Livewire.on('propertiesUpdated', initSearchMap);
    // Or, if the script is re-rendered with the component:
    
    // This will run when the component is initially loaded or re-rendered by Livewire
    // The `livewire:navigated` handles full page navigation with Livewire.
    // For subsequent updates within the same page, this script block will be re-evaluated if it's part of the updated DOM.
    // Let's ensure it's called correctly.
    // We can use a Livewire hook that fires after an update.
    
    Livewire.hook('morph.updated', ({ el, component }) => {
        // Check if this specific component was updated
        if (component.name === 'property-search') {
             // Ensure map is initialized/updated only if map view is active
            if (component.snapshot.data.layout_view === 'map') {
                 // Use timeout to ensure DOM is fully patched
                setTimeout(initSearchMap, 0);
            } else if (searchMapInstance) {
                // If not in map view and map exists, maybe hide or clear it
                propertyMarkers.forEach(marker => searchMapInstance.removeLayer(marker));
                propertyMarkers = [];
                // searchMapInstance.invalidateSize(); // If map div was hidden/shown
            }
        }
    });
    
    // Initial call if viewMode is already 'map' on load (e.g. from query string)
    // Use $wire to access Livewire component's public properties from JS
    // Ensure this script runs after Livewire is initialized for $wire to be available.
    // The livewire:navigated and morph.updated hooks should ensure this.
    // The initial call is tricky if $wire is not yet defined.
    // Alpine's viewMode is a good source of truth for initial state if this script runs after Alpine.
    // Let's use a small timeout to ensure Alpine has initialized viewMode.
    

    setTimeout(() => {
        const alpineComponent = document.querySelector('[x-data]');
        if (alpineComponent && alpineComponent.__x && alpineComponent.__x.$data.viewMode === 'map') {
            initSearchMap();
        }
    }, 0);


</script>
@endpush

<x-guest-layout>
    <form method="POST" action="{{ route('password.store') }}">
        @csrf

        <!-- Password Reset Token -->
        <input type="hidden" name="token" value="{{ $request->route('token') }}">

        <!-- Email Address -->
        <div>
            <flux:input id="email" class="block mt-1 w-full" type="email" name="email" label="{{ __('Email') }}" :value="old('email', $request->email)" required autofocus autocomplete="username" />
        </div>

        <!-- Password -->
        <div class="mt-4">
            <flux:input id="password" class="block mt-1 w-full" type="password" name="password" label="{{ __('Password') }}" required autocomplete="new-password" />
        </div>

        <!-- Confirm Password -->
        <div class="mt-4">
            <flux:input id="password_confirmation" class="block mt-1 w-full"
                                type="password"
                                name="password_confirmation" label="{{ __('Confirm Password') }}" required autocomplete="new-password" />
        </div>

        <div class="flex items-center justify-end mt-4">
            <flux:button variant="primary">
                {{ __('Reset Password') }}
            </x-primary-button>
        </div>
    </form>
</x-guest-layout>
